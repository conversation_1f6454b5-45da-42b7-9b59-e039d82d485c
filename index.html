<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>TRIADA CTF 2025 Announcement</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <!-- FontAwesome for icons (optional, can remove if not needed) -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <!-- Your main stylesheet -->
  <link rel="stylesheet" href="style.css">
</head>
<body>
  <header>
    <a href="#" class="brand">Triada</a>
    <nav>
      <ul>
        <li><a href="#details">Event</a></li>
        <li><a href="#categories">Categories</a></li>
        <li><a href="#challenges">Teasers</a></li>
        <li><a href="#rules">Rules</a></li>
        <li><a href="#registration">Register</a></li>
      </ul>
    </nav>
  </header>

  <main class="container home-container" style="flex-direction:column;align-items:center;justify-content:flex-start;">
    <section class="logo-section" style="width:100%;text-align:center;margin-top:80px;display:flex;flex-direction:column;align-items:center;">
      <div class="logo-background" style="margin:0 auto;max-width:220px;">
        <img src="https://triada.in/images/logo.svg" alt="TRIADA CTF Logo" style="width:100%;max-width:220px;display:block;margin:0 auto 18px auto;">
      </div>
      <span class="main-logo" style="font-size:2.2rem;display:block;margin-bottom:8px;">TRIADA CTF 2025</span>
      <div class="community-partner-label" style="margin-top:10px;">Hack the Impossible. Win the Glory.</div>
    </section>

    <section class="content-section" style="width:100%;max-width:700px;margin:0 auto;margin-top:30px;">
      <div class="description" style="text-align:center;">
        <strong>Registration opens soon!</strong>
        <div id="countdown" style="font-size:2rem;color:var(--primary-red);margin:10px 0;"></div>
        <a href="#registration" class="btn btn-primary" style="margin:10px 0;">Register Now</a>
      </div>
    </section>

    <section id="details" class="page-container" style="max-width:700px;">
      <div class="page-header">
        <h1>Event Details</h1>
        <div class="subtitle">Everything you need to know</div>
      </div>
      <div class="event-details" style="display:grid;grid-template-columns:1fr 1fr;gap:1.2rem 2.5rem;margin-bottom:1.5rem;">
        <div><strong>Date & Time:</strong> 27 September 2025, 16:00 UTC</div>
        <div><strong>Duration:</strong> 36 hours</div>
        <div><strong>Platform:</strong> <a href="https://ctfd.io/" target="_blank" style="color:var(--primary-red);">CTFd</a></div>
        <div><strong>Participation:</strong> Teams (up to 4 members)</div>
        <div><strong>Prizes:</strong> <span style="color:var(--primary-red);">Cash prizes for Top 3 teams!</span></div>
      </div>
    </section>

    <section id="categories" class="page-container" style="max-width:700px;">
      <div class="page-header">
        <h1>Categories & Difficulty</h1>
      </div>
      <div class="categories" style="display:grid;grid-template-columns:repeat(auto-fit,minmax(120px,1fr));gap:1.5rem;text-align:center;">
        <div class="preview-card"><span class="card-icon">🔐</span>Crypto</div>
        <div class="preview-card"><span class="card-icon">🌐</span>Web</div>
        <div class="preview-card"><span class="card-icon">🕵️‍♂️</span>Forensics</div>
        <div class="preview-card"><span class="card-icon">🔄</span>Reverse Eng.</div>
        <div class="preview-card"><span class="card-icon">🎲</span>Misc</div>
      </div>
      <div class="difficulty" style="text-align:center;margin-top:1.5rem;">
        <span class="btn btn-secondary" style="margin:0 0.5rem;">Easy</span>
        <span class="btn btn-secondary" style="margin:0 0.5rem;">Medium</span>
        <span class="btn btn-secondary" style="margin:0 0.5rem;">Hard</span>
      </div>
    </section>

    <section id="challenges" class="page-container" style="max-width:700px;">
      <div class="page-header">
        <h1>Sample Challenges</h1>
      </div>
      <div class="sample-challenges" style="display:flex;flex-direction:column;gap:1.2rem;">
        <div class="preview-card">“Decrypt the locked message… if you dare.”</div>
        <div class="preview-card">“Escape the server before the trace hits 100%.”</div>
        <div class="preview-card">SH-RSA: “Can you break the shadowed RSA and reveal the secret?”</div>
      </div>
    </section>

    <section id="rules" class="page-container" style="max-width:700px;">
      <div class="page-header">
        <h1>Rules</h1>
      </div>
      <ul class="rules" style="margin-top:1.2rem;">
        <li>No sharing of flags or solutions.</li>
        <li>No attacking the competition infrastructure.</li>
        <li>Only one team per person.</li>
        <li>Respect other participants and organizers.</li>
        <li>Any violation may result in disqualification.</li>
      </ul>
    </section>

    <section id="registration" class="page-container" style="max-width:700px;">
      <div class="page-header">
        <h1>Registration</h1>
      </div>
      <div class="registration" style="text-align:center;">
        <a href="https://ctfd.io/" class="btn btn-primary" target="_blank">Join the CTF</a>
        <div class="reg-deadline" style="color:var(--primary-red);margin-top:0.7rem;">Registration closes: 26 September 2025, 23:59 UTC</div>
      </div>
    </section>
  </main>

  <footer>
    <div class="footer-content">
      <div class="footer-logo">TRIADA CTF</div>
      <div class="footer-links">
        <a href="mailto:<EMAIL>">Email</a>
        <a href="https://twitter.com/triadactf" target="_blank">Twitter</a>
        <a href="https://discord.gg/triadactf" target="_blank">Discord</a>
        <a href="https://github.com/triadactf" target="_blank">GitHub</a>
      </div>
    </div>
    <div class="footer-content" style="justify-content:center;">
      <span style="color:var(--primary-red);">Sponsored by RedTeam Labs &amp; CyberSec Corp</span>
    </div>
    <div class="footer-content" style="justify-content:center;">
      <span class="footer-copyright">&copy; 2025 TRIADA CTF. All rights reserved.</span>
    </div>
  </footer>

  <script>
    // Countdown Timer
    const countdown = document.getElementById('countdown');
    // Set your registration opening date/time here (UTC)
    const startDate = new Date('2025-09-20T16:00:00Z').getTime(); // Registration opens Sept 20
    function updateCountdown() {
      const now = new Date().getTime();
      const distance = startDate - now;
      if (distance < 0) {
        countdown.innerHTML = "Registration is OPEN!";
        return;
      }
      const days = Math.floor(distance / (1000 * 60 * 60 * 24));
      const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const mins = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
      const secs = Math.floor((distance % (1000 * 60)) / 1000);
      countdown.innerHTML = `${days}d ${hours}h ${mins}m ${secs}s`;
    }
    setInterval(updateCountdown, 1000);
    updateCountdown();
  </script>
</body>
</html>