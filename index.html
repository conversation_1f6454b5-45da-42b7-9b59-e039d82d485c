<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>TRIADA CTF 2025 Announcement</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <link rel="stylesheet" href="style.css">
</head>
<body>
  <header>
    <a href="https://triada.in/" class="brand">Triada</a>
    <nav>
      <input type="checkbox" id="nav-toggle" class="nav-toggle">
      <label for="nav-toggle" class="nav-toggle-label">
        <span></span>
      </label>
    </nav>
  </header>

  <main class="container home-container">
    <section class="logo-section">
      <div class="logo-background">
        <img src="https://triada.in/images/logo.svg" alt="TRIADA CTF Logo">
      </div>
      <h1 class="main-logo">TRIADA CTF 2025</h1>
      <div class="community-partner-label">Hack the Impossible. Win the Glory.</div>
    </section>

    <section class="content-section">
      <div class="description">
        <strong>Registration starts soon!</strong>
        <div id="countdown"></div>
      </div>
    </section>

    <section id="details" class="page-container">
      <div class="page-header">
        <h1>Event Details</h1>
        <div class="subtitle">Everything you need to know</div>
      </div>
      <div class="event-details">
        <div><strong>Date:</strong> 26 September 2025</div>
        <div><strong>Duration:</strong> 24 hours</div>
        <div><strong>Platform:</strong> <a href="https://labs.triada.in/" target="_blank" class="platform-link">Triada Labs (labs.triada.in)</a></div>
        <div><strong>Participation:</strong> Solo or Duo</div>
        <div><strong>Prize Money:</strong> <span class="prize-text">Cash prizes for Top 3 positions!</span></div>
        <div><strong>Difficulty:</strong> <span class="difficulty-text">Easy • Medium • Hard • Expert</span></div>
      </div>
    </section>

    <section id="categories" class="page-container">
      <div class="page-header">
        <h1>Categories & Difficulty</h1>
      </div>
      <div class="categories">
        <div class="preview-card">
          <span class="card-icon">🔐</span>
          <span class="card-title">Crypto</span>
        </div>
        <div class="preview-card">
          <span class="card-icon">🌐</span>
          <span class="card-title">Web</span>
        </div>
        <div class="preview-card">
          <span class="card-icon">🕵️‍♂️</span>
          <span class="card-title">Forensics</span>
        </div>
        <div class="preview-card">
          <span class="card-icon">🔄</span>
          <span class="card-title">Reverse Eng.</span>
        </div>
        <div class="preview-card">
          <span class="card-icon">🎲</span>
          <span class="card-title">Misc</span>
        </div>
      </div>
      <div class="difficulty">
        <span class="btn btn-secondary">Easy</span>
        <span class="btn btn-secondary">Medium</span>
        <span class="btn btn-secondary">Hard</span>
      </div>
    </section>


    

    <section id="registration" class="page-container">
      <div class="page-header">
      </div>
      <div class="registration">
        <div class="reg-deadline">Registration opens soon - Stay tuned!</div>
      </div>
    </section>
  </main>

  <footer>
    <div class="footer-content footer-copyright-container">
      <span class="footer-copyright">&copy; 2025 TRIADA CTF. All rights reserved.</span>
    </div>
  </footer>

  <script>
    // Countdown Timer
    const countdown = document.getElementById('countdown');
    // Set CTF date (26 September 2025)
    const ctfDate = new Date('2025-09-26T00:00:00Z').getTime();
    function updateCountdown() {
      const now = new Date().getTime();
      const distance = ctfDate - now;
      if (distance < 0) {
        countdown.innerHTML = "CTF is LIVE!";
        return;
      }
      const days = Math.floor(distance / (1000 * 60 * 60 * 24));
      const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const mins = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
      const secs = Math.floor((distance % (1000 * 60)) / 1000);
      countdown.innerHTML = `${days}d ${hours}h ${mins}m ${secs}s`;
    }
    setInterval(updateCountdown, 1000);
    updateCountdown();
  </script>
</body>
</html>