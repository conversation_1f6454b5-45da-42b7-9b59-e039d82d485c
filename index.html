<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>TRIADA CTF 2025 Announcement</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <link rel="stylesheet" href="style.css">
</head>
<body>
  <header>
    <a href="#" class="brand">Triada</a>
    <nav>
      <input type="checkbox" id="nav-toggle" class="nav-toggle">
      <label for="nav-toggle" class="nav-toggle-label">
        <span></span>
      </label>
      <ul>
        <li><a href="#details">Event</a></li>
        <li><a href="#categories">Categories</a></li>
        <li><a href="#challenges">Teasers</a></li>
        <li><a href="#rules">Rules</a></li>
        <li><a href="#registration">Register</a></li>
      </ul>
    </nav>
  </header>

  <main class="container home-container">
    <section class="logo-section">
      <div class="logo-background">
        <img src="https://triada.in/images/logo.svg" alt="TRIADA CTF Logo">
      </div>
      <h1 class="main-logo">TRIADA CTF 2025</h1>
      <div class="community-partner-label">Hack the Impossible. Win the Glory.</div>
    </section>

    <section class="content-section">
      <div class="description">
        <strong>Registration opens soon!</strong>
        <div id="countdown"></div>
        <a href="#registration" class="btn btn-primary">Register Now</a>
      </div>
    </section>

    <section id="details" class="page-container">
      <div class="page-header">
        <h1>Event Details</h1>
        <div class="subtitle">Everything you need to know</div>
      </div>
      <div class="event-details">
        <div><strong>Date & Time:</strong> 27 September 2025, 16:00 UTC</div>
        <div><strong>Duration:</strong> 36 hours</div>
        <div><strong>Platform:</strong> <a href="https://ctfd.io/" target="_blank" class="platform-link">CTFd</a></div>
        <div><strong>Participation:</strong> Teams (up to 4 members)</div>
        <div><strong>Prizes:</strong> <span class="prize-text">Cash prizes for Top 3 teams!</span></div>
      </div>
    </section>

    <section id="categories" class="page-container">
      <div class="page-header">
        <h1>Categories & Difficulty</h1>
      </div>
      <div class="categories">
        <div class="preview-card">
          <span class="card-icon">🔐</span>
          <span class="card-title">Crypto</span>
        </div>
        <div class="preview-card">
          <span class="card-icon">🌐</span>
          <span class="card-title">Web</span>
        </div>
        <div class="preview-card">
          <span class="card-icon">🕵️‍♂️</span>
          <span class="card-title">Forensics</span>
        </div>
        <div class="preview-card">
          <span class="card-icon">🔄</span>
          <span class="card-title">Reverse Eng.</span>
        </div>
        <div class="preview-card">
          <span class="card-icon">🎲</span>
          <span class="card-title">Misc</span>
        </div>
      </div>
      <div class="difficulty">
        <span class="btn btn-secondary">Easy</span>
        <span class="btn btn-secondary">Medium</span>
        <span class="btn btn-secondary">Hard</span>
      </div>
    </section>

    <section id="challenges" class="page-container">
      <div class="page-header">
        <h1>Sample Challenges</h1>
      </div>
      <div class="sample-challenges">
        <div class="preview-card">“Decrypt the locked message… if you dare.”</div>
        <div class="preview-card">“Escape the server before the trace hits 100%.”</div>
        <div class="preview-card">SH-RSA: “Can you break the shadowed RSA and reveal the secret?”</div>
      </div>
    </section>

    <section id="rules" class="page-container">
      <div class="page-header">
        <h1>Rules</h1>
      </div>
      <ul class="rules">
        <li>No sharing of flags or solutions.</li>
        <li>No attacking the competition infrastructure.</li>
        <li>Only one team per person.</li>
        <li>Respect other participants and organizers.</li>
        <li>Any violation may result in disqualification.</li>
      </ul>
    </section>

    <section id="registration" class="page-container">
      <div class="page-header">
        <h1>Registration</h1>
      </div>
      <div class="registration">
        <a href="https://ctfd.io/" class="btn btn-primary" target="_blank">Join the CTF</a>
        <div class="reg-deadline">Registration closes: 26 September 2025, 23:59 UTC</div>
      </div>
    </section>
  </main>

  <footer>
    <div class="footer-content">
      <div class="footer-logo">TRIADA CTF</div>
      <div class="footer-links">
        <a href="mailto:<EMAIL>">Email</a>
        <a href="https://twitter.com/triadactf" target="_blank">Twitter</a>
        <a href="https://discord.gg/triadactf" target="_blank">Discord</a>
        <a href="https://github.com/triadactf" target="_blank">GitHub</a>
      </div>
    </div>
    <div class="footer-content footer-sponsors">
      <span class="sponsor-text">Sponsored by RedTeam Labs &amp; CyberSec Corp</span>
    </div>
    <div class="footer-content footer-copyright-container">
      <span class="footer-copyright">&copy; 2025 TRIADA CTF. All rights reserved.</span>
    </div>
  </footer>

  <script>
    // Countdown Timer
    const countdown = document.getElementById('countdown');
    // Set your registration opening date/time here (UTC)
    const startDate = new Date('2025-09-20T16:00:00Z').getTime(); // Registration opens Sept 20
    function updateCountdown() {
      const now = new Date().getTime();
      const distance = startDate - now;
      if (distance < 0) {
        countdown.innerHTML = "Registration is OPEN!";
        return;
      }
      const days = Math.floor(distance / (1000 * 60 * 60 * 24));
      const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const mins = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
      const secs = Math.floor((distance % (1000 * 60)) / 1000);
      countdown.innerHTML = `${days}d ${hours}h ${mins}m ${secs}s`;
    }
    setInterval(updateCountdown, 1000);
    updateCountdown();
  </script>
</body>
</html>