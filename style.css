
@import url('https://fonts.cdnfonts.com/css/technos');
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');

:root {
    --primary-red: #ff0033;
    --dark-red: #cc0026;
    --black: #0a0a0a;
    --white: #fff;
    --gray: #333;
    --light-gray: #555;
    --very-light-gray: #999;
    --transparent-red: rgba(255, 0, 51, 0.1);
    --primary-font: 'Montserrat', sans-serif;
    --logo-font: 'Technos', sans-serif;
    --transition-speed: 0.4s;
    --glow-effect: 0 0 15px rgba(255, 0, 51, 0.5);
    --text-glow: 0 0 10px rgba(255, 0, 51, 0.3);
    --neon-border: 1px solid rgba(255, 0, 51, 0.3);
    --vh: 1vh;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
}

body {
    font-family: var(--primary-font);
    background-color: var(--black);
    color: var(--white);
    min-height: 100vh;
    overflow-x: hidden;
    line-height: 1.6;
    position: relative;
    font-size: 16px;
}

/* Prevent zoom on inputs */
input,
select,
textarea,
button {
    font-size: 16px;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        linear-gradient(90deg, rgba(255, 0, 51, 0.05) 1px, transparent 1px) 0 0 / 20px 20px,
        linear-gradient(rgba(255, 0, 51, 0.05) 1px, transparent 1px) 0 0 / 20px 20px;
    pointer-events: none;
    z-index: 0;
}

body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 50% 50%, rgba(255, 0, 51, 0.05) 0%, transparent 70%);
    pointer-events: none;
    z-index: 0;
}

/* Header and Navigation */
header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: var(--neon-border);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
    clip-path: polygon(0 0, 100% 0, 100% 85%, 95% 100%, 0 100%);
}

.brand {
    font-family: var(--logo-font);
    font-size: 24px;
    font-weight: bold;
    color: var(--white);
    position: relative;
    z-index: 1001;
    letter-spacing: 1px;
    text-shadow: var(--text-glow);
    padding: 10px 20px;
    border: var(--neon-border);
    clip-path: polygon(0 0, 100% 0, 95% 100%, 5% 100%);
    text-decoration: none;
    display: block;
    transition: all var(--transition-speed);
}

.brand:hover {
    color: var(--white);
    text-shadow: 0 0 15px rgba(255, 0, 51, 0.7);
    border-color: rgba(255, 0, 51, 0.5);
}

nav {
    position: relative;
}

.nav-toggle {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.nav-toggle-label {
    display: none;
}

nav ul {
    display: flex;
    list-style: none;
    gap: 30px;
    padding: 10px 20px;
    background: rgba(10, 10, 10, 0.8);
    border: var(--neon-border);
    clip-path: polygon(5% 0, 100% 0, 95% 100%, 0 100%);
}

nav a {
    color: var(--white);
    text-decoration: none;
    font-family: var(--primary-font);
    font-size: 16px;
    font-weight: 500;
    transition: all var(--transition-speed);
    padding: 5px 0;
    position: relative;
    letter-spacing: 0.5px;
}

nav a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: var(--primary-red);
    transition: width var(--transition-speed);
    box-shadow: var(--glow-effect);
}

nav a:hover::after,
nav a.active::after {
    width: 100%;
}

nav a:hover,
nav a.active {
    color: var(--primary-red);
    text-shadow: var(--text-glow);
}

/* Main Content */
.container {
    width: 100%;
    min-height: 100vh;
    display: flex;
    padding-top: 80px;
    position: relative;
    z-index: 1;
}

.home-container {
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding: 50px;
    min-height: calc(100vh - 80px);
    margin-top: 0;
    display: flex;
}

.logo-section {
    width: 100%;
    text-align: center;
    margin-top: 80px;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    overflow: hidden;
    background: transparent;
}

.logo-background {
    margin: 0 auto;
    max-width: 220px;
    position: relative;
    background: url('images/stroke.png') no-repeat center center;
    background-size: contain;
    mix-blend-mode: normal;
    filter: drop-shadow(0 0 10px rgba(255, 0, 51, 0.3));
    margin-bottom: 18px;
}

.logo-background img {
    width: 100%;
    max-width: 220px;
    display: block;
    margin: 0 auto 18px auto;
}

.community-partner-label {
    font-family: var(--primary-font);
    font-size: 18px;
    color: var(--primary-red);
    background: rgba(255, 0, 51, 0.08);
    border: 1px solid rgba(255, 0, 51, 0.2);
    border-radius: 18px;
    padding: 6px 22px;
    margin-top: 10px;
    letter-spacing: 1px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(255, 0, 51, 0.07);
    display: inline-block;
    position: relative;
    z-index: 2;
}

.content-section {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    margin-top: 30px;
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    background: rgba(10, 10, 10, 0.8);
    border: var(--neon-border);
    clip-path: polygon(5% 0, 100% 0, 95% 100%, 0 100%);
    position: relative;
}

.content-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent, rgba(255, 0, 51, 0.05), transparent);
    animation: shine 8s ease-in-out infinite;
}

@keyframes shine {
    0% {
        transform: translateX(-200%);
        opacity: 0;
    }

    20% {
        opacity: 0.5;
    }

    80% {
        opacity: 0.5;
    }

    100% {
        transform: translateX(200%);
        opacity: 0;
    }
}

.main-logo {
    font-family: var(--primary-font);
    font-size: 2.2rem;
    display: block;
    margin-bottom: 8px;
    position: relative;
    letter-spacing: 2px;
    text-shadow: var(--text-glow);
    animation: glowPulse 2s infinite alternate;
}

@keyframes glowPulse {
    0% {
        text-shadow: 0 0 10px rgba(255, 0, 51, 0.3);
    }

    100% {
        text-shadow: 0 0 20px rgba(255, 0, 51, 0.5);
    }
}

.description {
    text-align: center;
    font-size: 18px;
    line-height: 1.8;
    margin-bottom: 40px;
    font-weight: 300;
    color: var(--white);
    position: relative;
    padding: 20px 25px 25px;
    background: rgba(20, 20, 20, 0.5);
    border: var(--neon-border);
    clip-path: polygon(5% 0, 100% 0, 95% 100%, 0 100%);
    z-index: 1;
    overflow: visible;
}

#countdown {
    font-size: 2rem;
    color: var(--primary-red);
    margin: 10px 0;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    margin-top: 10px;
    flex-wrap: wrap;
}

.btn {
    display: inline-block;
    padding: 12px 30px;
    border: none;
    border-radius: 0;
    font-family: var(--primary-font);
    font-size: 16px;
    font-weight: 600;
    text-transform: uppercase;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-speed);
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
    clip-path: polygon(5% 0, 100% 0, 95% 100%, 0 100%);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background-color: var(--primary-red);
    color: var(--white);
    box-shadow: var(--glow-effect);
    border: var(--neon-border);
}

.btn-secondary {
    background-color: transparent;
    color: var(--white);
    border: var(--neon-border);
    box-shadow: var(--glow-effect);
}

.btn-primary:hover {
    background-color: var(--dark-red);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(255, 0, 51, 0.4);
}

.btn-secondary:hover {
    background-color: var(--transparent-red);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(255, 0, 51, 0.4);
}

/* Page Containers */
.page-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 100px 50px 80px;
}

.page-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 0 20px;
}

.page-header h1 {
    font-family: var(--primary-font);
    font-size: 48px;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
    letter-spacing: 2px;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    max-width: 100%;
}

.page-header h1::after {
    content: '';
    position: absolute;
    width: 60px;
    height: 3px;
    background-color: var(--primary-red);
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
}

.subtitle {
    font-size: 18px;
    color: var(--very-light-gray);
    font-weight: 300;
    margin-top: 15px;
    margin-bottom: 15px;
}

/* Event Details */
.event-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem 3rem;
    margin-bottom: 2rem;
}

.platform-link {
    color: var(--primary-red);
}

.prize-text {
    color: var(--primary-red);
    font-weight: 600;
}

.difficulty-text {
    color: var(--white);
    font-weight: 500;
}

/* Categories */
.categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
    text-align: center;
    margin-bottom: 2rem;
}

.preview-card {
    background: rgba(20, 20, 20, 0.5);
    border: var(--neon-border);
    padding: 25px;
    text-align: center;
    transition: all var(--transition-speed);
    clip-path: polygon(5% 0, 100% 0, 95% 100%, 0 100%);
    min-height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.preview-card:hover {
    background: rgba(255, 0, 51, 0.1);
    transform: translateY(-2px);
}

.card-icon {
    font-size: 2rem;
    display: block;
    margin-bottom: 10px;
}

.card-title {
    display: block;
    font-weight: 500;
}

.difficulty {
    text-align: center;
    margin-top: 1.5rem;
}

.difficulty .btn {
    margin: 0 0.5rem;
}

/* Sample Challenges */
.sample-challenges {
    display: flex;
    flex-direction: column;
    gap: 1.2rem;
}

/* Rules */
.rules {
    margin-top: 1.2rem;
    list-style: none;
    padding: 0;
}

.rules li {
    padding: 10px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    padding-left: 20px;
}

.rules li:before {
    content: "•";
    color: var(--primary-red);
    position: absolute;
    left: 0;
}

/* Registration */
.registration {
    text-align: center;
}

.reg-deadline {
    color: var(--primary-red);
    margin-top: 0.7rem;
}




/* Footer */
footer {
    background-color: rgba(10, 10, 10, 0.95);
    padding: 40px 0;
    border-top: var(--neon-border);
    box-shadow: 0 -2px 20px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    clip-path: polygon(0 15%, 5% 0, 100% 0, 100% 100%, 0 100%);
    width: 100%;
    position: relative;
    z-index: 10;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.footer-logo {
    font-family: var(--primary-font);
    font-size: 24px;
    letter-spacing: 1px;
    text-shadow: var(--text-glow);
    padding: 10px 20px;
    border: var(--neon-border);
    clip-path: polygon(5% 0, 100% 0, 95% 100%, 0 100%);
}

.footer-links {
    display: flex;
    gap: 20px;
    padding: 10px 20px;
    background: rgba(20, 20, 20, 0.5);
    border: var(--neon-border);
    clip-path: polygon(5% 0, 100% 0, 95% 100%, 0 100%);
}

.footer-links a {
    color: var(--white);
    text-decoration: none;
    transition: all var(--transition-speed);
    font-weight: 500;
}

.footer-links a:hover {
    color: var(--primary-red);
    text-shadow: var(--text-glow);
}

.footer-copyright {
    color: var(--very-light-gray);
    font-size: 14px;
}

.footer-sponsors {
    justify-content: center;
}

.sponsor-text {
    color: var(--primary-red);
}

.footer-copyright-container {
    justify-content: center;
}

/* Blogs Page Responsive Design */
@media screen and (max-width: 900px) {
    .blogs-hero {
        padding: 30px 20px 20px;
    }

    .hero-title {
        font-size: 32px;
        font-weight: 700;
        letter-spacing: 0.8px;
    }

    .hero-subtitle {
        font-size: 15px;
    }

    .content-wrapper {
        gap: 24px;
    }

    .status-card {
        padding: 20px;
    }

    .status-header {
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }

    .status-icon {
        margin: 0 auto;
        width: 40px;
        height: 40px;
        font-size: 18px;
    }

    .preview-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .section-title {
        font-size: 20px;
        margin-bottom: 20px;
    }

    .notification-card {
        padding: 20px;
    }
}

@media screen and (max-width: 600px) {
    .blogs-hero {
        padding: 25px 16px 20px;
    }

    .hero-title {
        font-size: 28px;
        font-weight: 700;
        letter-spacing: 0.6px;
    }

    .hero-subtitle {
        font-size: 14px;
    }

    .hero-badge {
        padding: 5px 14px;
        font-size: 11px;
    }

    .blogs-main {
        padding: 20px 16px 30px;
    }

    .content-wrapper {
        gap: 20px;
    }

    .status-card {
        padding: 18px 16px;
        border-radius: 12px;
    }

    .status-icon {
        width: 36px;
        height: 36px;
        font-size: 16px;
        border-radius: 10px;
    }

    .status-text h3 {
        font-size: 16px;
    }

    .status-text p {
        font-size: 13px;
    }

    .section-title {
        font-size: 18px;
        margin-bottom: 16px;
    }

    .preview-card {
        padding: 16px 14px;
        border-radius: 12px;
    }

    .card-icon {
        width: 36px;
        height: 36px;
        font-size: 16px;
        border-radius: 10px;
        margin-bottom: 10px;
    }

    .preview-card h4 {
        font-size: 15px;
        margin-bottom: 6px;
    }

    .preview-card p {
        font-size: 12px;
    }

    .notification-card {
        padding: 18px 16px;
        border-radius: 12px;
    }

    .notification-card h3 {
        font-size: 16px;
    }

    .notification-card p {
        font-size: 13px;
        margin-bottom: 16px;
    }

    .social-links {
        gap: 10px;
    }

    .social-link {
        width: 36px;
        height: 36px;
        font-size: 14px;
        border-radius: 8px;
    }

    .logo-section {
        min-height: 120px;
        height: 160px;
        padding-top: 30px;
    }

    .logo-background {
        width: 100%;
        height: 180px;
        min-height: 120px;
        max-width: 100vw;
        max-height: 220px;
        background-size: contain !important;
        background-position: center !important;
        background-repeat: no-repeat !important;
        display: block;
    }
}

/* Responsive Design */
@media screen and (max-width: 1200px) {
    .team-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media screen and (max-width: 1024px) {
    .home-container {
        flex-direction: column;
        padding: 20px;
        height: auto;
        justify-content: flex-start;
    }

    .logo-section {
        width: 100%;
        height: 400px;
        margin-bottom: 30px;
        align-self: center;
    }

    .content-section {
        width: 100%;
        margin-left: 0;
        margin-bottom: 30px;
        align-self: center;
    }

    .page-container {
        padding: 100px 30px 80px;
    }

    .event-details {
        grid-template-columns: 1fr;
        gap: 1.2rem;
    }

    .categories {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 1.5rem;
    }

    .main-logo {
        font-size: 48px;
    }

    .description {
        font-size: 16px;
    }
}

@media screen and (max-width: 900px) {
    .achievements-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        padding: 30px;
        gap: 25px;
    }

    .achievement-card {
        padding: 25px;
    }
}

@media screen and (max-width: 768px) {
    .container {
        padding-top: 80px;
    }

    header {
        padding: 12px 15px;
        clip-path: none;
        background-color: rgba(10, 10, 10, 0.98);
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
        height: auto;
    }

    .brand {
        font-size: 20px;
        padding: 6px 12px;
        border: none;
        text-shadow: 0 0 10px rgba(255, 0, 51, 0.5);
    }

    nav ul {
        position: fixed;
        top: 0;
        right: -100%;
        flex-direction: column;
        width: 100%;
        height: 100vh;
        /* Fallback */
        height: calc(var(--vh, 1vh) * 100);
        /* Use custom viewport height */
        background: rgba(10, 10, 10, 0.98);
        z-index: 1001;
        justify-content: center;
        align-items: center;
        padding: 80px 30px 30px;
        transition: all 0.3s ease;
        box-shadow: -5px 0 20px rgba(0, 0, 0, 0.5);
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        /* Smooth scrolling on iOS */
    }

    .home-container {
        padding: 15px;
        gap: 20px;
        margin-top: 0;
    }

    .logo-section {
        height: 200px;
        margin: 0;
        margin-top: 80px;
        align-self: center;
    }

    .logo-background {
        width: 100%;
        height: 100%;
        background-size: contain;
        background-position: center;
    }

    .content-section {
        width: 100%;
        margin: 0;
        padding: 20px;
        clip-path: none;
        align-self: center;
        border: none;
        background-color: transparent;
    }

    .description {
        font-size: 16px;
        line-height: 1.6;
        padding: 15px 20px;
        background-color: rgba(20, 20, 20, 0.7);
        border: var(--neon-border);
        border-radius: 4px;
        margin-bottom: 25px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    }

    .cta-buttons {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 10px;
        width: 100%;
        justify-content: center;
    }

    .social-links-home {
        justify-content: center;
        margin-bottom: 20px;
        flex-wrap: wrap;
    }

    .social-icon-home {
        margin: 0 12px;
        font-size: 22px;
    }

    .btn {
        width: calc(50% - 5px);
        text-align: center;
        clip-path: none;
        padding: 12px;
        border-radius: 4px;
    }

    .main-logo {
        font-size: 42px;
        text-align: center;
        width: 100%;
        margin-bottom: 20px;
    }

    /* Footer mobile optimization */
    footer {
        clip-path: none;
        padding: 30px 0;
        width: 100%;
    }

    .footer-content {
        padding: 0 20px;
        text-align: center;
        justify-content: center;
    }

    .footer-logo {
        clip-path: none;
        padding: 8px 15px;
    }

    .footer-links {
        width: 100%;
        justify-content: center;
        clip-path: none;
        padding: 10px;
    }

    .team-grid {
        grid-template-columns: 1fr;
        padding: 15px;
        gap: 25px;
    }

    .team-status-legend {
        margin-bottom: 30px;
        padding: 12px 20px;
    }

    .nav-toggle-label {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        position: relative;
        z-index: 1002;
        width: 35px;
        height: 35px;
        margin-right: 5px;
        background-color: rgba(20, 20, 20, 0.5);
        border-radius: 4px;
        border: 1px solid rgba(255, 0, 51, 0.2);
    }

    .nav-toggle-label span,
    .nav-toggle-label span::before,
    .nav-toggle-label span::after {
        display: block;
        position: absolute;
        height: 2px;
        width: 22px;
        background-color: var(--primary-red);
        transition: all 0.3s ease;
        border-radius: 2px;
    }

    .nav-toggle-label span {
        top: 50%;
        transform: translateY(-50%);
    }

    .nav-toggle-label span::before,
    .nav-toggle-label span::after {
        content: '';
    }

    .nav-toggle-label span::before {
        top: -7px;
    }

    .nav-toggle-label span::after {
        top: 7px;
    }

    .nav-toggle:checked~.nav-toggle-label {
        background-color: rgba(255, 0, 51, 0.1);
    }

    .nav-toggle:checked~.nav-toggle-label span {
        background: transparent;
    }

    .nav-toggle:checked~.nav-toggle-label span::before {
        transform: rotate(45deg);
        top: 0;
        background-color: var(--white);
    }

    .nav-toggle:checked~.nav-toggle-label span::after {
        transform: rotate(-45deg);
        top: 0;
        background-color: var(--white);
    }

    .nav-toggle:checked~ul {
        right: 0;
        clip-path: none;
        box-shadow: -5px 0 20px rgba(0, 0, 0, 0.5);
    }
}

@media screen and (max-width: 576px) {
    .container {
        padding-top: 80px;
    }

    .home-container {
        padding: 15px;
    }

    .logo-section {
        height: 160px;
        margin-top: 60px;
    }

    .main-logo {
        font-size: 36px;
        margin-bottom: 15px;
    }

    .description {
        font-size: 16px;
        padding: 18px;
        border-radius: 4px;
        overflow: visible;
        margin-bottom: 25px;
        line-height: 1.7;
    }

    nav ul {
        gap: 15px;
        padding: 80px 20px 20px;
    }

    nav a {
        font-size: 20px;
        padding: 15px 0;
        display: block;
        width: 100%;
        text-align: center;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    nav a::after {
        width: 0 !important;
        height: 0;
    }

    nav a.active {
        color: var(--primary-red);
        border-bottom: 2px solid var(--primary-red);
        text-shadow: var(--text-glow);
    }

    nav li {
        width: 100%;
    }

    .btn {
        font-size: 16px;
        padding: 14px;
        width: 100%;
        margin-bottom: 10px;
        letter-spacing: 1px;
    }

    .social-icon-home {
        margin: 0 12px;
        font-size: 24px;
    }

    .footer-links {
        flex-direction: column;
        gap: 15px;
        margin: 15px 0;
    }

    .footer-links a {
        padding: 10px;
        font-size: 16px;
    }

    .footer-logo {
        font-size: 26px;
        margin-bottom: 10px;
    }

    .footer-copyright {
        font-size: 14px;
        margin-top: 15px;
    }

    .social-links-footer {
        margin: 15px 0;
    }

    .social-icon-footer {
        font-size: 22px;
        margin: 0 10px;
    }

    .achievements-grid {
        grid-template-columns: 1fr;
        padding: 20px 15px;
        gap: 20px;
    }

    .achievement-card {
        padding: 25px;
        clip-path: none;
        border-radius: 8px;
        transform: translateY(20px);
        margin-bottom: 25px;
        border: 1px solid rgba(255, 0, 51, 0.2);
    }

    .achievement-card.visible {
        transform: translateY(0);
    }

    .achievement-card:hover {
        transform: translateY(-5px) scale(1.01);
    }

    .achievement-card h3 {
        font-size: 22px;
        margin-bottom: 15px;
    }

    .achievement-card p {
        font-size: 16px;
        line-height: 1.7;
    }

    .page-header {
        margin-bottom: 40px;
        padding: 0 15px;
        text-align: center;
    }

    .page-header h1 {
        font-size: 28px;
        margin-bottom: 15px;
        word-wrap: break-word;
        overflow-wrap: break-word;
        hyphens: auto;
    }

    .subtitle {
        font-size: 18px;
        line-height: 1.5;
    }

    .page-container {
        padding: 100px 20px 40px;
    }

    .event-details {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .categories {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 1rem;
    }

    .team-member {
        margin: 0 0 20px 0;
        clip-path: none;
        border-radius: 8px;
        overflow: hidden;
    }

    .member-image {
        height: 250px;
    }

    .member-info {
        padding: 20px;
    }

    .member-info h3 {
        font-size: 22px;
        margin-bottom: 8px;
    }

    .member-title {
        font-size: 16px;
        margin-bottom: 12px;
    }

    .member-bio {
        font-size: 15px;
        line-height: 1.6;
    }

    .member-social {
        margin-top: 15px;
    }

    .social-icon {
        font-size: 20px;
        margin-right: 18px;
    }


}

/* Landscape mode optimization */
@media screen and (max-height: 600px) and (orientation: landscape) {
    .container {
        padding-top: 70px;
    }

    .home-container {
        flex-direction: row;
        align-items: center;
        padding: 15px;
        min-height: auto;
        gap: 15px;
    }

    .logo-section {
        width: 35%;
        height: 150px;
        margin: 0;
        margin-top: 0;
    }

    .content-section {
        width: 60%;
        padding: 15px;
        border: none;
        background-color: transparent;
    }

    .main-logo {
        font-size: 28px;
        margin-bottom: 10px;
        text-align: left;
    }

    .description {
        font-size: 14px;
        margin-bottom: 15px;
        padding: 12px;
    }

    .social-links-home {
        margin-bottom: 15px;
    }

    .cta-buttons {
        flex-direction: row;
        gap: 10px;
    }

    .btn {
        padding: 8px 15px;
        font-size: 14px;
        width: auto;
    }
}


/* Fix red overlay that was removed */
.red-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, rgba(255, 0, 51, 0.1), rgba(0, 0, 0, 0.1));
    mix-blend-mode: multiply;
    pointer-events: none;
}

/* Fix page container spacing */
.page-container {
    padding-top: 100px;
}

/* Fix image loading optimization */
img {
    max-width: 100%;
    height: auto;
}

/* Performance improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Fix for iOS Safari issues */
@supports (-webkit-touch-callout: none) {

    /* Fix for 100vh issue in iOS Safari */
    .home-container,
    .page-container {
        padding-bottom: 80px;
        min-height: -webkit-fill-available;
    }

    body,
    html {
        height: -webkit-fill-available;
    }

    nav ul {
        height: -webkit-fill-available;
        max-height: 100vh;
    }

    /* Fix for input zoom */
    input[type="text"],
    input[type="email"],
    input[type="password"],
    input[type="number"],
    input[type="tel"],
    select,
    textarea {
        font-size: 16px !important;
    }
}

/* --- Make Triada logo larger --- */
.logo-background {
    width: 95%;
    height: 95%;
    max-width: 520px;
    max-height: 520px;
}

@media screen and (max-width: 600px) {
    .logo-background {
        width: 100%;
        height: 180px;
        min-height: 120px;
        max-width: 100vw;
        max-height: 220px;
    }

    .community-partner-label,
    .logo-section a[href*="c0c0n"] {
        display: none !important;
    }

    .community-partner-mobile {
        display: block !important;
        margin: 24px auto 0 auto;
        text-align: center;
    }

    .community-partner-mobile .community-partner-label,
    .community-partner-mobile a {
        display: block !important;
    }
}

